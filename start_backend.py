#!/usr/bin/env python3
"""
Script untuk men<PERSON> backend SIBI Sign Language Detection API
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if all required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import cv2
        import numpy
        import ultralytics
        import torch
        logger.info("All required packages are installed")
        return True
    except ImportError as e:
        logger.error(f"Missing required package: {e}")
        return False

def check_model_file():
    """Check if model file exists"""
    model_path = Path("./model/sibiv3.pt")
    if model_path.exists():
        logger.info(f"Model file found: {model_path}")
        return True
    else:
        logger.error(f"Model file not found: {model_path}")
        logger.error("Please ensure your YOLOv11 model file is placed at ./model/sibiv3.pt")
        return False

def install_requirements():
    """Install required packages"""
    logger.info("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def main():
    """Main function to start the backend server"""
    logger.info("Starting SIBI Sign Language Detection Backend...")
    
    # Check if model file exists
    if not check_model_file():
        sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        logger.info("Installing missing requirements...")
        if not install_requirements():
            sys.exit(1)
        
        # Check again after installation
        if not check_requirements():
            logger.error("Failed to install all requirements")
            sys.exit(1)
    
    # Change to backend directory
    backend_dir = Path("./backend")
    if not backend_dir.exists():
        logger.error("Backend directory not found")
        sys.exit(1)
    
    # Start the server
    try:
        logger.info("Starting FastAPI server on http://localhost:8000")
        logger.info("WebSocket endpoint: ws://localhost:8000/ws")
        logger.info("Press Ctrl+C to stop the server")
        
        os.chdir(backend_dir)
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
